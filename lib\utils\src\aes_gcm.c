/**
 * @file aes_gcm.c
 * @brief AES-GCM encryption/decryption implementation for TuyaOpen protocol 2.3
 * 
 * This file implements AES-GCM authenticated encryption/decryption using ESP-IDF's mbedtls library.
 * It provides the cipher wrapper functions needed for TuyaOpen protocol 2.3 compatibility.
 */

#include "aes_gcm.h"
#include "tuya_error_code.h"
#include "tuya_log.h"
#include "system_interface.h"

#include "mbedtls/gcm.h"
#include "mbedtls/error.h"

/**
 * @brief AES-GCM authenticated encryption wrapper
 * 
 * @param params Cipher parameters including key, nonce, additional data, and plaintext
 * @param output Buffer to store encrypted data
 * @param olen Pointer to store output length
 * @param tag Buffer to store authentication tag
 * @param tag_len Length of authentication tag
 * @return OPERATE_RET Operation result
 */
OPERATE_RET mbedtls_cipher_auth_encrypt_wrapper(const cipher_params_t *params,
                                                uint8_t *output, size_t *olen,
                                                uint8_t *tag, size_t tag_len)
{
    if (!params || !output || !olen || !tag) {
        TY_LOGE("Invalid parameters");
        return OPRT_INVALID_PARM;
    }

    if (params->cipher_type != MBEDTLS_CIPHER_AES_128_GCM) {
        TY_LOGE("Unsupported cipher type: %d", params->cipher_type);
        return OPRT_COM_ERROR;
    }

    mbedtls_gcm_context gcm;
    mbedtls_gcm_init(&gcm);

    int ret = mbedtls_gcm_setkey(&gcm, MBEDTLS_CIPHER_ID_AES, params->key, params->key_len * 8);
    if (ret != 0) {
        TY_LOGE("mbedtls_gcm_setkey failed: -0x%04x", -ret);
        mbedtls_gcm_free(&gcm);
        return OPRT_COM_ERROR;
    }

    ret = mbedtls_gcm_crypt_and_tag(&gcm, MBEDTLS_GCM_ENCRYPT,
                                    params->data_len,
                                    params->nonce, params->nonce_len,
                                    params->ad, params->ad_len,
                                    params->data, output,
                                    tag_len, tag);

    mbedtls_gcm_free(&gcm);

    if (ret != 0) {
        TY_LOGE("mbedtls_gcm_crypt_and_tag encrypt failed: -0x%04x", -ret);
        return OPRT_COM_ERROR;
    }

    *olen = params->data_len;
    return OPRT_OK;
}

/**
 * @brief AES-GCM authenticated decryption wrapper
 * 
 * @param params Cipher parameters including key, nonce, additional data, and ciphertext
 * @param output Buffer to store decrypted data
 * @param olen Pointer to store output length
 * @param tag Authentication tag to verify
 * @param tag_len Length of authentication tag
 * @return OPERATE_RET Operation result
 */
OPERATE_RET mbedtls_cipher_auth_decrypt_wrapper(const cipher_params_t *params,
                                                uint8_t *output, size_t *olen,
                                                const uint8_t *tag, size_t tag_len)
{
    if (!params || !output || !olen || !tag) {
        TY_LOGE("Invalid parameters");
        return OPRT_INVALID_PARM;
    }

    if (params->cipher_type != MBEDTLS_CIPHER_AES_128_GCM) {
        TY_LOGE("Unsupported cipher type: %d", params->cipher_type);
        return OPRT_COM_ERROR;
    }

    mbedtls_gcm_context gcm;
    mbedtls_gcm_init(&gcm);

    int ret = mbedtls_gcm_setkey(&gcm, MBEDTLS_CIPHER_ID_AES, params->key, params->key_len * 8);
    if (ret != 0) {
        TY_LOGE("mbedtls_gcm_setkey failed: -0x%04x", -ret);
        mbedtls_gcm_free(&gcm);
        return OPRT_COM_ERROR;
    }

    ret = mbedtls_gcm_auth_decrypt(&gcm, params->data_len,
                                   params->nonce, params->nonce_len,
                                   params->ad, params->ad_len,
                                   tag, tag_len,
                                   params->data, output);

    mbedtls_gcm_free(&gcm);

    if (ret != 0) {
        if (ret == MBEDTLS_ERR_GCM_AUTH_FAILED) {
            TY_LOGE("GCM authentication failed - invalid tag");
        } else {
            TY_LOGE("mbedtls_gcm_auth_decrypt failed: -0x%04x", -ret);
        }
        return OPRT_COM_ERROR;
    }

    *olen = params->data_len;
    return OPRT_OK;
}
