# TuyaOpen API版本修复总结

## 问题描述

在TuyaOpen迁移过程中，发现版本更新API仍然报错：
```
ERROR atop_base.c:269: errorMsg:API or API version error
```

日志显示请求仍然使用v4.4版本：
```
v=4.4&sign=86f3a027d3331b5de2e35bc22a7c8148
```

## 根本原因

虽然代码中调用的是 `atop_service_version_update_v41()` 函数，但该函数内部仍然使用了错误的版本号。

## 修复内容

### 1. atop_service.c 修复
修复了以下API的版本号：

| API | 原版本 | 修复后版本 | 说明 |
|-----|--------|------------|------|
| `tuya.device.reset` | 4.4 | 4.0 | 设备重置API |
| `tuya.device.versions.update` | 4.4 | 4.1 | 版本更新API |
| `tuya.device.upgrade.get` | 4.4 | 4.1 | 升级获取API |
| `tuya.device.upgrade.silent.get` | 4.4 | 4.1 | 静默升级API |
| `tuya.device.upgrade.status.update` | 4.4 | 4.1 | 升级状态更新API |

### 2. matop_service.c 修复
同样修复了MQTT ATOP服务中的相同API版本号。

## 修复的具体位置

### atop_service.c
- 第186行：`tuya.device.reset` 版本 4.4 → 4.0
- 第305行：`tuya.device.upgrade.get` 版本 4.4 → 4.1
- 第351行：`tuya.device.upgrade.silent.get` 版本 4.4 → 4.1
- 第398行：`tuya.device.upgrade.status.update` 版本 4.4 → 4.1
- 第457行：`tuya.device.versions.update` 版本 4.4 → 4.1

### matop_service.c
- 第359行：`tuya.device.reset` 版本 4.4 → 4.0
- 第395行：`tuya.device.versions.update` 版本 4.4 → 4.1
- 第431行：`tuya.device.upgrade.status.update` 版本 4.4 → 4.1
- 第467行：`tuya.device.upgrade.get` 版本 4.4 → 4.1
- 第504行：`tuya.device.upgrade.silent.get` 版本 4.4 → 4.1

## 预期结果

修复后，版本更新API应该能够正常工作，不再出现 "API or API version error" 错误。

## 测试建议

1. 重新编译并运行设备
2. 观察版本更新API的日志，确认使用正确的版本号
3. 验证不再出现API版本错误

## 状态

- ✅ 设备激活成功
- ✅ MQTT连接成功
- ✅ AES-GCM解密支持完整实现
- ⏳ 版本更新API修复（待测试验证）
